@echo off
echo Syncing files from root to public folder...

:: Copy assets
xcopy /E /Y assets public\assets\

:: Copy auth
xcopy /E /Y auth public\auth\

:: Copy classes  
xcopy /E /Y classes public\classes\

:: Copy rankings
xcopy /E /Y rankings public\rankings\

:: Copy research
xcopy /E /Y research public\research\

:: Copy achievements
xcopy /E /Y achievements public\achievements\

:: Copy admin
xcopy /E /Y admin public\admin\

:: Copy main index.html
copy /Y index.html public\index.html

echo Sync completed successfully!
echo You can now deploy with: firebase deploy
pause

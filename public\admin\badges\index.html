<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON>n <PERSON> - Vthon Academy</title>
    <link rel="stylesheet" href="../../assets/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Admin Badge Management Styles */
        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            margin-top: 100px; /* Add space for fixed header */
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: calc(100vh - 100px);
        }

        .admin-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .admin-header h1 {
            color: #2d3748;
            margin: 0 0 10px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .admin-header p {
            color: #718096;
            margin: 0;
        }

        .admin-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .admin-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f7fafc;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }

        /* Student Selection */
        .student-search {
            position: relative;
            margin-bottom: 20px;
        }

        .student-search input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .student-search input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .student-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            background: white;
        }

        .student-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f7fafc;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .student-item:hover {
            background: #f7fafc;
        }

        .student-item.selected {
            background: #ebf8ff;
            border-left: 4px solid #667eea;
        }

        .student-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .student-info {
            flex: 1;
        }

        .student-name {
            font-weight: 500;
            color: #2d3748;
            margin: 0;
        }

        .student-email {
            font-size: 0.85rem;
            color: #718096;
            margin: 0;
        }

        /* Badge Selection */
        .badges-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .badge-option {
            text-align: center;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .badge-option:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }

        .badge-option.selected {
            border-color: #667eea;
            background: #ebf8ff;
        }

        .badge-image {
            width: 60px;
            height: 60px;
            margin: 0 auto 10px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid #e2e8f0;
        }

        .badge-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .badge-name {
            font-weight: 600;
            color: #2d3748;
            margin: 0 0 5px 0;
            font-size: 0.9rem;
        }

        .badge-description {
            font-size: 0.8rem;
            color: #718096;
            line-height: 1.3;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            font-size: 0.95rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f7fafc;
            color: #4a5568;
            border: 2px solid #e2e8f0;
        }

        .btn-secondary:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        /* Success/Error Messages */
        .message {
            padding: 15px 20px;
            border-radius: 10px;
            margin: 20px 0;
            display: none;
            align-items: center;
            gap: 10px;
        }

        .message.success {
            background: #f0fff4;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .message.error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }

        @media (max-width: 768px) {
            .admin-content {
                grid-template-columns: 1fr;
            }
            
            .badges-grid {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../index.html">Trang Chủ</a></li>
                    <li><a href="../../classes/">Lớp Học</a></li>
                    <li><a href="../../achievements/">Thành Tích</a></li>
                    <li><a href="../../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../../research/">Nghiên Cứu</a></li>
                    <li><a href="../index.html" class="active">Quản Trị</a></li>
                    <li><a href="../../auth/"><i class="fas fa-user"></i> Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Admin Container -->
    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <h1>
                <i class="fas fa-medal"></i>
                Quản Lý Huy Hiệu
            </h1>
            <p>Trao tặng huy hiệu cho học viên xuất sắc</p>
        </div>

        <!-- Success/Error Messages -->
        <div id="successMessage" class="message success">
            <i class="fas fa-check-circle"></i>
            <span id="successText"></span>
        </div>
        
        <div id="errorMessage" class="message error">
            <i class="fas fa-exclamation-circle"></i>
            <span id="errorText"></span>
        </div>

        <!-- Content -->
        <div class="admin-content">
            <!-- Student Selection -->
            <div class="admin-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="card-title">Chọn Học Viên</h3>
                </div>
                
                <div class="student-search">
                    <input type="text" id="studentSearch" placeholder="Tìm kiếm học viên...">
                </div>
                
                <div class="student-list" id="studentList">
                    <!-- Students will be populated by JavaScript -->
                </div>
            </div>

            <!-- Badge Selection -->
            <div class="admin-card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <h3 class="card-title">Chọn Huy Hiệu</h3>
                </div>
                
                <div class="badges-grid" id="badgesGrid">
                    <!-- Badges will be populated by JavaScript -->
                </div>
                
                <div class="action-buttons">
                    <button class="btn btn-primary" id="awardBadgeBtn" disabled>
                        <i class="fas fa-award"></i>
                        Trao Huy Hiệu
                    </button>
                    <button class="btn btn-secondary" id="revokeBadgeBtn" disabled>
                        <i class="fas fa-times"></i>
                        Thu Hồi Huy Hiệu
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Common JavaScript -->
    <script src="../../assets/js/script.js"></script>

    <!-- Firebase and JavaScript -->
    <script type="module">
        // Firebase imports
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js';
        import { getAuth, onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js';
        import { getFirestore, doc, getDoc, setDoc, updateDoc, collection, getDocs, query, where, arrayUnion, arrayRemove } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Global variables
        let selectedStudent = null;
        let selectedBadge = null;
        let availableBadges = [];
        let allStudents = [];

        // Check admin access
        onAuthStateChanged(auth, async (user) => {
            if (user && user.email === '<EMAIL>') {
                await loadStudents();
                await loadBadges();
            } else {
                // Redirect non-admin users
                window.location.href = '../../auth/';
            }
        });

        // Load all students
        async function loadStudents() {
            try {
                const usersSnapshot = await getDocs(collection(db, "users"));
                allStudents = [];
                
                usersSnapshot.forEach((doc) => {
                    const userData = doc.data();
                    if (userData.email !== '<EMAIL>') { // Exclude admin
                        allStudents.push({
                            id: doc.id,
                            ...userData
                        });
                    }
                });

                displayStudents(allStudents);
                setupStudentSearch();
            } catch (error) {
                console.error("Error loading students:", error);
                showError("Không thể tải danh sách học viên");
            }
        }

        // Display students
        function displayStudents(students) {
            const studentList = document.getElementById('studentList');
            
            if (students.length === 0) {
                studentList.innerHTML = `
                    <div style="text-align: center; padding: 20px; color: #718096;">
                        <i class="fas fa-users" style="font-size: 2rem; margin-bottom: 10px;"></i>
                        <p>Không có học viên nào</p>
                    </div>
                `;
                return;
            }

            studentList.innerHTML = students.map(student => `
                <div class="student-item" data-student-id="${student.id}">
                    <div class="student-avatar">
                        ${student.fullName ? student.fullName.charAt(0).toUpperCase() : student.email.charAt(0).toUpperCase()}
                    </div>
                    <div class="student-info">
                        <div class="student-name">${student.fullName || 'Chưa cập nhật'}</div>
                        <div class="student-email">${student.email}</div>
                    </div>
                </div>
            `).join('');

            // Add click handlers
            document.querySelectorAll('.student-item').forEach(item => {
                item.addEventListener('click', () => selectStudent(item));
            });
        }

        // Setup student search
        function setupStudentSearch() {
            const searchInput = document.getElementById('studentSearch');
            searchInput.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                const filteredStudents = allStudents.filter(student => 
                    (student.fullName && student.fullName.toLowerCase().includes(searchTerm)) ||
                    student.email.toLowerCase().includes(searchTerm)
                );
                displayStudents(filteredStudents);
            });
        }

        // Select student
        function selectStudent(studentElement) {
            // Remove previous selection
            document.querySelectorAll('.student-item').forEach(item => {
                item.classList.remove('selected');
            });

            // Add selection to clicked item
            studentElement.classList.add('selected');
            
            const studentId = studentElement.dataset.studentId;
            selectedStudent = allStudents.find(s => s.id === studentId);
            
            updateActionButtons();
        }

        // Load available badges
        async function loadBadges() {
            try {
                const response = await fetch('../../assets/images/badges/badges.json');
                const badgesData = await response.json();
                availableBadges = badgesData.badges;
                
                displayBadges();
            } catch (error) {
                console.error("Error loading badges:", error);
                showError("Không thể tải danh sách huy hiệu");
            }
        }

        // Display badges
        function displayBadges() {
            const badgesGrid = document.getElementById('badgesGrid');
            
            badgesGrid.innerHTML = availableBadges.map(badge => `
                <div class="badge-option" data-badge-id="${badge.id}">
                    <div class="badge-image ${badge.rarity}">
                        <img src="../../assets/images/badges/${badge.image}" alt="${badge.name}" 
                             onerror="this.src='../../assets/images/logo.jpg'">
                    </div>
                    <div class="badge-name">${badge.name}</div>
                    <div class="badge-description">${badge.description}</div>
                </div>
            `).join('');

            // Add click handlers
            document.querySelectorAll('.badge-option').forEach(option => {
                option.addEventListener('click', () => selectBadge(option));
            });
        }

        // Select badge
        function selectBadge(badgeElement) {
            // Remove previous selection
            document.querySelectorAll('.badge-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Add selection to clicked item
            badgeElement.classList.add('selected');
            
            const badgeId = badgeElement.dataset.badgeId;
            selectedBadge = availableBadges.find(b => b.id === badgeId);
            
            updateActionButtons();
        }

        // Update action buttons state
        function updateActionButtons() {
            const awardBtn = document.getElementById('awardBadgeBtn');
            const revokeBtn = document.getElementById('revokeBadgeBtn');
            
            const canAct = selectedStudent && selectedBadge;
            awardBtn.disabled = !canAct;
            revokeBtn.disabled = !canAct;
            
            if (canAct) {
                const studentBadges = selectedStudent.badges || [];
                const hasBadge = studentBadges.includes(selectedBadge.id);
                
                awardBtn.style.display = hasBadge ? 'none' : 'flex';
                revokeBtn.style.display = hasBadge ? 'flex' : 'none';
            } else {
                awardBtn.style.display = 'flex';
                revokeBtn.style.display = 'none';
            }
        }

        // Award badge
        async function awardBadge() {
            if (!selectedStudent || !selectedBadge) return;
            
            try {
                const userRef = doc(db, "users", selectedStudent.id);
                await updateDoc(userRef, {
                    badges: arrayUnion(selectedBadge.id)
                });
                
                // Update local data
                selectedStudent.badges = selectedStudent.badges || [];
                selectedStudent.badges.push(selectedBadge.id);
                
                showSuccess(`Đã trao huy hiệu "${selectedBadge.name}" cho ${selectedStudent.fullName || selectedStudent.email}`);
                updateActionButtons();
                
            } catch (error) {
                console.error("Error awarding badge:", error);
                showError("Không thể trao huy hiệu. Vui lòng thử lại.");
            }
        }

        // Revoke badge
        async function revokeBadge() {
            if (!selectedStudent || !selectedBadge) return;
            
            try {
                const userRef = doc(db, "users", selectedStudent.id);
                await updateDoc(userRef, {
                    badges: arrayRemove(selectedBadge.id)
                });
                
                // Update local data
                selectedStudent.badges = selectedStudent.badges || [];
                selectedStudent.badges = selectedStudent.badges.filter(id => id !== selectedBadge.id);
                
                showSuccess(`Đã thu hồi huy hiệu "${selectedBadge.name}" từ ${selectedStudent.fullName || selectedStudent.email}`);
                updateActionButtons();
                
            } catch (error) {
                console.error("Error revoking badge:", error);
                showError("Không thể thu hồi huy hiệu. Vui lòng thử lại.");
            }
        }

        // Show success message
        function showSuccess(message) {
            const successMsg = document.getElementById('successMessage');
            const successText = document.getElementById('successText');
            
            successText.textContent = message;
            successMsg.style.display = 'flex';
            
            setTimeout(() => {
                successMsg.style.display = 'none';
            }, 5000);
        }

        // Show error message
        function showError(message) {
            const errorMsg = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            errorText.textContent = message;
            errorMsg.style.display = 'flex';
            
            setTimeout(() => {
                errorMsg.style.display = 'none';
            }, 5000);
        }

        // Event listeners
        document.getElementById('awardBadgeBtn').addEventListener('click', awardBadge);
        document.getElementById('revokeBadgeBtn').addEventListener('click', revokeBadge);
    </script>
</body>
</html>

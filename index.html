<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vthon - Lớ<PERSON> học lập trình <PERSON> và AI</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="index.html" class="active">Trang Chủ</a></li>
                    <li><a href="classes/">Lớp Học</a></li>
                    <li><a href="achievements/">Thành Tích</a></li>
                    <li><a href="auth/register.html">Đăng Ký</a></li>
                    <li><a href="rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="research/">Nghiên Cứu</a></li>
                    <li><a href="auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section with Full Screen Background -->
    <section class="hero">
        <div class="hero-content">
            <div class="typing-container">
                <h1 class="typing-text">Chào mừng bạn đến với VThon Academy</h1>
            </div>

            <div class="scroll-indicator" data-aos="fade-up" data-aos-delay="800">
                <p class="scroll-text">Khám phá thế giới Python và AI cùng chúng tôi</p>
                <div class="scroll-arrow">
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </div>

        <!-- Floating Elements -->
        <div class="floating-elements">
            <div class="floating-element" style="--delay: 0s; --duration: 3s;">💻</div>
            <div class="floating-element" style="--delay: 1s; --duration: 4s;">🐍</div>
            <div class="floating-element" style="--delay: 2s; --duration: 3.5s;">🤖</div>
            <div class="floating-element" style="--delay: 0.5s; --duration: 4.5s;">📚</div>
            <div class="floating-element" style="--delay: 1.5s; --duration: 3.2s;">⚡</div>
        </div>
    </section>

    <!-- Code Camp Event Section -->
    <section class="code-camp-event" id="code-camp">
        <div class="container">
            <div class="event-header" data-aos="fade-up">
                <div class="event-badge">
                    <i class="fas fa-fire"></i>
                    <span>SỰ KIỆN HOT</span>
                </div>
                <h2 class="event-title">
                    <span class="gradient-text">Code Camp Hè 2025</span>
                    <div class="title-decoration">
                        <div class="decoration-line"></div>
                        <i class="fas fa-trophy"></i>
                        <div class="decoration-line"></div>
                    </div>
                </h2>
                <p class="event-subtitle">Cuộc thi lập trình hè dành cho học viên Python</p>
            </div>

            <div class="event-content">
                <div class="event-info" data-aos="fade-right">
                    <div class="info-card">
                        <div class="info-header">
                            <i class="fas fa-calendar-alt"></i>
                            <h3>Thời Gian Diễn Ra</h3>
                        </div>
                        <div class="date-range">
                            <div class="date-item">
                                <span class="date-label">Bắt đầu</span>
                                <span class="date-value">14/06/2025</span>
                            </div>
                            <div class="date-separator">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                            <div class="date-item">
                                <span class="date-label">Kết thúc</span>
                                <span class="date-value">01/08/2025</span>
                            </div>
                        </div>
                    </div>

                    <div class="info-card">
                        <div class="info-header">
                            <i class="fas fa-users"></i>
                            <h3>Đối Tượng Tham Gia</h3>
                        </div>
                        <p>Tất cả học viên đang theo học các lớp Python tại Vthon Academy</p>
                    </div>

                    <div class="info-card">
                        <div class="info-header">
                            <i class="fas fa-chart-line"></i>
                            <h3>Cách Thức Tham Gia</h3>
                        </div>
                        <p>Điểm số trên <strong>Bảng Xếp Hạng</strong> sẽ quyết định thứ hạng cuối cùng. Hãy hoàn thành các bài tập để tích lũy điểm!</p>
                    </div>
                </div>

                <div class="prizes-section" data-aos="fade-left">
                    <h3 class="prizes-title">
                        <i class="fas fa-gift"></i>
                        Giải Thưởng Hấp Dẫn
                    </h3>

                    <div class="prizes-grid">
                        <div class="prize-card top1" data-aos="zoom-in" data-aos-delay="100">
                            <div class="prize-rank">
                                <span class="rank-number">1</span>
                                <span class="rank-text">TOP</span>
                            </div>
                            <div class="prize-badge">
                                <img src="assets/images/badges/top1_cuoc_thi_moi.png" alt="Top 1 Badge" class="badge-image">
                                <div class="badge-glow"></div>
                            </div>
                            <div class="prize-details">
                                <h4>Huy Hiệu Độc Quyền</h4>
                                <div class="prize-value">150,000 VNĐ</div>
                                <p>Phần thưởng tiền mặt</p>
                            </div>
                        </div>

                        <div class="prize-card top2" data-aos="zoom-in" data-aos-delay="200">
                            <div class="prize-rank">
                                <span class="rank-number">2</span>
                                <span class="rank-text">TOP</span>
                            </div>
                            <div class="prize-badge">
                                <img src="assets/images/badges/top2_cuoc_thi_moi.png" alt="Top 2 Badge" class="badge-image">
                                <div class="badge-glow"></div>
                            </div>
                            <div class="prize-details">
                                <h4>Huy Hiệu Độc Quyền</h4>
                                <div class="prize-value">100,000 VNĐ</div>
                                <p>Phần thưởng tiền mặt</p>
                            </div>
                        </div>

                        <div class="prize-card top3" data-aos="zoom-in" data-aos-delay="300">
                            <div class="prize-rank">
                                <span class="rank-number">3</span>
                                <span class="rank-text">TOP</span>
                            </div>
                            <div class="prize-badge">
                                <img src="assets/images/badges/top3_cuoc_thi_moi.png" alt="Top 3 Badge" class="badge-image">
                                <div class="badge-glow"></div>
                            </div>
                            <div class="prize-details">
                                <h4>Huy Hiệu Độc Quyền</h4>
                                <div class="prize-value">50,000 VNĐ</div>
                                <p>Phần thưởng tiền mặt</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="event-cta" data-aos="fade-up" data-aos-delay="400">
                <a href="rankings/" class="btn btn-event">
                    <i class="fas fa-chart-line"></i>
                    Xem Bảng Xếp Hạng
                </a>
                <a href="classes/" class="btn btn-secondary">
                    <i class="fas fa-code"></i>
                    Tham Gia Ngay
                </a>
            </div>
        </div>

        <!-- Animated Background Elements -->
        <div class="event-bg-elements">
            <div class="bg-element trophy" style="--delay: 0s;">🏆</div>
            <div class="bg-element code" style="--delay: 1s;">💻</div>
            <div class="bg-element fire" style="--delay: 2s;">🔥</div>
            <div class="bg-element star" style="--delay: 0.5s;">⭐</div>
            <div class="bg-element rocket" style="--delay: 1.5s;">🚀</div>
        </div>
    </section>

    <!-- Courses Section -->
    <section class="courses-section" id="courses">
        <div class="container">
            <div class="courses-header" data-aos="fade-up">
                <h2 class="courses-title">
                    <span class="gradient-text">Khóa Học Của Chúng Tôi</span>
                    <div class="title-decoration">
                        <div class="decoration-line"></div>
                        <i class="fas fa-graduation-cap"></i>
                        <div class="decoration-line"></div>
                    </div>
                </h2>
                <p class="courses-subtitle">Chọn khóa học phù hợp với bạn và bắt đầu hành trình học tập</p>
            </div>

            <div class="courses-grid">
                <!-- Python-AI Course -->
                <div class="course-card python-course" data-aos="fade-up" data-aos-delay="100">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fab fa-python"></i>
                        </div>
                        <div class="course-badge">HOT</div>
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Python - AI từ Cơ Bản đến Nâng Cao</h3>
                        <div class="course-details">
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>Học sinh THCS - THPT</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>250,000 VNĐ/tháng</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>8 buổi/tháng, 90 phút/buổi</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-video"></i>
                                <span>Online qua Google Meet</span>
                            </div>
                            <div class="trial-offer">
                                <i class="fas fa-gift"></i>
                                <span>Học thử 2 buổi miễn phí</span>
                            </div>
                        </div>
                        <div class="course-features">
                            <span class="feature-tag">🐍 Python</span>
                            <span class="feature-tag">🤖 AI/ML</span>
                            <span class="feature-tag">💻 Coding</span>
                        </div>
                    </div>
                    <div class="course-footer">
                        <a href="auth/register.html" class="btn btn-course">Đăng Ký Học Thử</a>
                    </div>
                </div>

                <!-- Scratch Course -->
                <div class="course-card scratch-course" data-aos="fade-up" data-aos-delay="200">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-puzzle-piece"></i>
                        </div>
                        <div class="course-badge">NEW</div>
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Scratch - Tin Học Cơ Bản</h3>
                        <div class="course-details">
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>Học sinh Tiểu học</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>300,000 VNĐ/tháng</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>8 buổi/tháng, 90 phút/buổi</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-video"></i>
                                <span>Online qua Google Meet</span>
                            </div>
                            <div class="trial-offer">
                                <i class="fas fa-gift"></i>
                                <span>Học thử 2 buổi miễn phí</span>
                            </div>
                        </div>
                        <div class="course-features">
                            <span class="feature-tag">🎨 Scratch</span>
                            <span class="feature-tag">📊 Excel</span>
                            <span class="feature-tag">📝 Word</span>
                            <span class="feature-tag">🖼️ Canva</span>
                        </div>
                    </div>
                    <div class="course-footer">
                        <a href="auth/register.html" class="btn btn-course">Đăng Ký Học Thử</a>
                    </div>
                </div>

                <!-- STEM Course -->
                <div class="course-card stem-course" data-aos="fade-up" data-aos-delay="300">
                    <div class="course-header">
                        <div class="course-icon">
                            <i class="fas fa-microscope"></i>
                        </div>
                        <div class="course-badge">PRO</div>
                    </div>
                    <div class="course-content">
                        <h3 class="course-title">Hỗ Trợ Nghiên Cứu KHKT - STEM</h3>
                        <div class="course-details">
                            <div class="detail-item">
                                <i class="fas fa-users"></i>
                                <span>Học sinh THCS - THPT</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-money-bill-wave"></i>
                                <span>350,000 VNĐ/tháng</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-clock"></i>
                                <span>8 buổi/tháng, 90 phút/buổi</span>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-video"></i>
                                <span>Online qua Google Meet</span>
                            </div>
                            <div class="detail-item special">
                                <i class="fas fa-star"></i>
                                <span>4 buổi code + 4 buổi slides</span>
                            </div>
                            <div class="trial-offer">
                                <i class="fas fa-gift"></i>
                                <span>Học thử 2 buổi miễn phí</span>
                            </div>
                        </div>
                        <div class="course-features">
                            <span class="feature-tag">🔬 KHKT</span>
                            <span class="feature-tag">📊 STEM</span>
                            <span class="feature-tag">📋 Báo cáo</span>
                            <span class="feature-tag">🏆 Dự án</span>
                        </div>
                    </div>
                    <div class="course-footer">
                        <a href="auth/register.html" class="btn btn-course">Đăng Ký Học Thử</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Animated Background Elements -->
        <div class="courses-bg-elements">
            <div class="bg-element book" style="--delay: 0s;">📚</div>
            <div class="bg-element graduation" style="--delay: 1s;">🎓</div>
            <div class="bg-element lightbulb" style="--delay: 2s;">💡</div>
            <div class="bg-element computer" style="--delay: 0.5s;">💻</div>
            <div class="bg-element science" style="--delay: 1.5s;">🔬</div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import the functions you need from the SDKs
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-analytics.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const auth = getAuth(app);

        // Check if user is logged in
        onAuthStateChanged(auth, (user) => {
            const loginButton = document.querySelector('.hero .btn');
            
            if (user) {
                // User is logged in
                loginButton.textContent = 'Vào Trang Cá Nhân';
            } else {
                // User is not logged in
                loginButton.textContent = 'Đăng Nhập';
            }
        });
    </script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });
    </script>

    <!-- Scroll Indicator Script -->
    <script>
        // Smooth scroll to next section when clicking scroll indicator
        document.querySelector('.scroll-arrow').addEventListener('click', function() {
            const nextSection = document.querySelector('#code-camp');
            if (nextSection) {
                nextSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    </script>

    <script src="assets/js/script.js"></script>
</body>
</html>
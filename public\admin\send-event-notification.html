<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>G<PERSON><PERSON>ng <PERSON><PERSON> - Admin</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .notification-form {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #FFD700;
        }
        
        .form-group textarea {
            height: 120px;
            resize: vertical;
        }
        
        .send-btn {
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
        }
        
        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .progress {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e2e8f0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #FFD700, #FF8C00);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }
        
        .status-message.success {
            background: rgba(76, 217, 100, 0.1);
            color: #2ca745;
            border: 1px solid #2ca745;
        }
        
        .status-message.error {
            background: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="../classes/">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="index.html" class="active">Quản Trị</a></li>
                    <li><a href="../auth/"><i class="fas fa-user"></i> Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <div class="admin-container">
        <div class="admin-header">
            <h1><i class="fas fa-bullhorn"></i> Gửi Thông Báo Sự Kiện</h1>
            <p>Gửi thông báo Code Camp Hè 2025 đến tất cả học viên</p>
        </div>

        <div class="notification-form">
            <form id="eventNotificationForm">
                <div class="form-group">
                    <label for="eventTitle">Tiêu đề thông báo:</label>
                    <input type="text" id="eventTitle" value="🔥 Sự kiện Code Camp Hè 2025 - Cơ hội nhận giải thưởng lớn!" readonly>
                </div>

                <div class="form-group">
                    <label for="eventContent">Nội dung thông báo:</label>
                    <textarea id="eventContent" readonly>🏆 THÔNG BÁO SỰ KIỆN ĐẶC BIỆT 🏆

Chào mừng bạn đến với sự kiện "Code Camp Hè 2025" - cuộc thi lập trình hè dành riêng cho các học viên Python tại Vthon Academy!

📅 Thời gian: 14/06/2025 - 01/08/2025
🎯 Đối tượng: Tất cả học viên các lớp Python
📊 Cách thức: Dựa trên điểm số Bảng Xếp Hạng

🎁 GIẢI THƯỞNG HẤP DẪN:
🥇 TOP 1: Huy hiệu độc quyền + 150,000 VNĐ
🥈 TOP 2: Huy hiệu độc quyền + 100,000 VNĐ  
🥉 TOP 3: Huy hiệu độc quyền + 50,000 VNĐ

Hãy hoàn thành các bài tập để tích lũy điểm và giành lấy vị trí cao nhất!

Chúc bạn may mắn! 🚀</textarea>
                </div>

                <button type="submit" class="send-btn" id="sendBtn">
                    <i class="fas fa-paper-plane"></i> Gửi Thông Báo Đến Tất Cả Học Viên
                </button>
            </form>

            <div class="progress" id="progressContainer">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div id="progressText">Đang gửi thông báo...</div>
            </div>

            <div class="status-message" id="statusMessage"></div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="../assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase functions
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, collection, getDocs, addDoc, query, where, serverTimestamp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Check admin access
        onAuthStateChanged(auth, (user) => {
            if (!user || user.email !== '<EMAIL>') {
                alert('Bạn không có quyền truy cập trang này!');
                window.location.href = '../auth/';
                return;
            }
        });

        // Send event notification to all students
        async function sendEventNotification() {
            const sendBtn = document.getElementById('sendBtn');
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const statusMessage = document.getElementById('statusMessage');

            try {
                // Disable button and show progress
                sendBtn.disabled = true;
                progressContainer.style.display = 'block';
                statusMessage.style.display = 'none';

                // Get all students (users with courseClass)
                const usersQuery = query(
                    collection(db, "users"),
                    where("courseClass", "in", ["python-a", "python-b", "python-c"])
                );
                
                const usersSnapshot = await getDocs(usersQuery);
                const students = [];
                
                usersSnapshot.forEach((doc) => {
                    const userData = doc.data();
                    if (userData.fullName && userData.fullName.trim() !== '') {
                        students.push({
                            id: doc.id,
                            name: userData.fullName,
                            email: userData.email
                        });
                    }
                });

                if (students.length === 0) {
                    throw new Error('Không tìm thấy học viên nào để gửi thông báo');
                }

                // Send notification to each student
                const title = document.getElementById('eventTitle').value;
                const content = document.getElementById('eventContent').value;
                let sentCount = 0;

                for (let i = 0; i < students.length; i++) {
                    const student = students[i];
                    
                    // Create message document
                    await addDoc(collection(db, "messages"), {
                        recipientId: student.id,
                        recipientName: student.name,
                        recipientEmail: student.email,
                        title: title,
                        content: content,
                        type: "event",
                        senderName: "Vthon Academy",
                        senderId: "admin",
                        unread: true,
                        createdAt: serverTimestamp()
                    });

                    sentCount++;
                    
                    // Update progress
                    const progress = (sentCount / students.length) * 100;
                    progressFill.style.width = progress + '%';
                    progressText.textContent = `Đã gửi ${sentCount}/${students.length} thông báo...`;
                    
                    // Small delay to prevent overwhelming Firebase
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                // Show success message
                statusMessage.className = 'status-message success';
                statusMessage.textContent = `✅ Đã gửi thành công thông báo đến ${sentCount} học viên!`;
                statusMessage.style.display = 'block';
                
                progressText.textContent = `Hoàn thành! Đã gửi ${sentCount} thông báo.`;

            } catch (error) {
                console.error('Error sending notifications:', error);
                
                // Show error message
                statusMessage.className = 'status-message error';
                statusMessage.textContent = `❌ Lỗi gửi thông báo: ${error.message}`;
                statusMessage.style.display = 'block';
                
                progressContainer.style.display = 'none';
            } finally {
                sendBtn.disabled = false;
            }
        }

        // Form submit handler
        document.getElementById('eventNotificationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (confirm('Bạn có chắc chắn muốn gửi thông báo sự kiện đến tất cả học viên?')) {
                sendEventNotification();
            }
        });
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>

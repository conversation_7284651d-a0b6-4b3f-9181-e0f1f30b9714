<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vthon - Lớ<PERSON> học lập trình <PERSON> và AI</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="assets/images/logo.jpg" alt="VTA Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="index.html" class="active">Trang Chủ</a></li>
                    <li><a href="classes/">Lớp Học</a></li>
                    <li><a href="achievements/">Thành Tích</a></li>
                    <li><a href="auth/register.html">Đăng Ký</a></li>
                    <li><a href="rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="research/">Nghiên Cứu</a></li>
                    <li><a href="auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section with Full Screen Background -->
    <section class="hero">
        <div class="hero-content">
            <div class="typing-container">
                <h1 class="typing-text">Chào mừng bạn đến với VT Academy</h1>
            </div>
            <p class="fade-in-up">Khám phá thế giới Python và AI cùng chúng tôi</p>

            <!-- Features Grid -->
            <div class="features-grid">
                <div class="feature-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon">
                        <i class="fab fa-python"></i>
                    </div>
                    <h3>Python Programming</h3>
                    <p>Học lập trình Python từ cơ bản đến nâng cao</p>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3>Artificial Intelligence</h3>
                    <p>Khám phá thế giới AI và Machine Learning</p>
                </div>

                <div class="feature-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Học Tập Tương Tác</h3>
                    <p>Môi trường học tập trực tuyến hiện đại</p>
                </div>
            </div>

            <div class="cta-section" data-aos="fade-up" data-aos-delay="400">
                <a href="auth/" class="btn btn-primary pulse">Đăng Nhập</a>
                <a href="classes/" class="btn btn-secondary">Xem Khóa Học</a>
            </div>
        </div>

        <!-- Floating Elements -->
        <div class="floating-elements">
            <div class="floating-element" style="--delay: 0s; --duration: 3s;">💻</div>
            <div class="floating-element" style="--delay: 1s; --duration: 4s;">🐍</div>
            <div class="floating-element" style="--delay: 2s; --duration: 3.5s;">🤖</div>
            <div class="floating-element" style="--delay: 0.5s; --duration: 4.5s;">📚</div>
            <div class="floating-element" style="--delay: 1.5s; --duration: 3.2s;">⚡</div>
        </div>
    </section>

    <!-- Code Camp Event Section -->
    <section class="code-camp-event" id="code-camp">
        <div class="container">
            <div class="event-header" data-aos="fade-up">
                <div class="event-badge">
                    <i class="fas fa-fire"></i>
                    <span>SỰ KIỆN HOT</span>
                </div>
                <h2 class="event-title">
                    <span class="gradient-text">Code Camp Hè 2025</span>
                    <div class="title-decoration">
                        <div class="decoration-line"></div>
                        <i class="fas fa-trophy"></i>
                        <div class="decoration-line"></div>
                    </div>
                </h2>
                <p class="event-subtitle">Cuộc thi lập trình hè dành cho học viên Python</p>
            </div>

            <div class="event-content">
                <div class="event-info" data-aos="fade-right">
                    <div class="info-card">
                        <div class="info-header">
                            <i class="fas fa-calendar-alt"></i>
                            <h3>Thời Gian Diễn Ra</h3>
                        </div>
                        <div class="date-range">
                            <div class="date-item">
                                <span class="date-label">Bắt đầu</span>
                                <span class="date-value">14/06/2025</span>
                            </div>
                            <div class="date-separator">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                            <div class="date-item">
                                <span class="date-label">Kết thúc</span>
                                <span class="date-value">01/08/2025</span>
                            </div>
                        </div>
                    </div>

                    <div class="info-card">
                        <div class="info-header">
                            <i class="fas fa-users"></i>
                            <h3>Đối Tượng Tham Gia</h3>
                        </div>
                        <p>Tất cả học viên đang theo học các lớp Python tại Vthon Academy</p>
                    </div>

                    <div class="info-card">
                        <div class="info-header">
                            <i class="fas fa-chart-line"></i>
                            <h3>Cách Thức Tham Gia</h3>
                        </div>
                        <p>Điểm số trên <strong>Bảng Xếp Hạng</strong> sẽ quyết định thứ hạng cuối cùng. Hãy hoàn thành các bài tập để tích lũy điểm!</p>
                    </div>
                </div>

                <div class="prizes-section" data-aos="fade-left">
                    <h3 class="prizes-title">
                        <i class="fas fa-gift"></i>
                        Giải Thưởng Hấp Dẫn
                    </h3>

                    <div class="prizes-grid">
                        <div class="prize-card top1" data-aos="zoom-in" data-aos-delay="100">
                            <div class="prize-rank">
                                <span class="rank-number">1</span>
                                <span class="rank-text">TOP</span>
                            </div>
                            <div class="prize-badge">
                                <img src="assets/images/badges/top1_cuoc_thi_moi.png" alt="Top 1 Badge" class="badge-image">
                                <div class="badge-glow"></div>
                            </div>
                            <div class="prize-details">
                                <h4>Huy Hiệu Độc Quyền</h4>
                                <div class="prize-value">150,000 VNĐ</div>
                                <p>Phần thưởng tiền mặt</p>
                            </div>
                        </div>

                        <div class="prize-card top2" data-aos="zoom-in" data-aos-delay="200">
                            <div class="prize-rank">
                                <span class="rank-number">2</span>
                                <span class="rank-text">TOP</span>
                            </div>
                            <div class="prize-badge">
                                <img src="assets/images/badges/top2_cuoc_thi_moi.png" alt="Top 2 Badge" class="badge-image">
                                <div class="badge-glow"></div>
                            </div>
                            <div class="prize-details">
                                <h4>Huy Hiệu Độc Quyền</h4>
                                <div class="prize-value">100,000 VNĐ</div>
                                <p>Phần thưởng tiền mặt</p>
                            </div>
                        </div>

                        <div class="prize-card top3" data-aos="zoom-in" data-aos-delay="300">
                            <div class="prize-rank">
                                <span class="rank-number">3</span>
                                <span class="rank-text">TOP</span>
                            </div>
                            <div class="prize-badge">
                                <img src="assets/images/badges/top3_cuoc_thi_moi.png" alt="Top 3 Badge" class="badge-image">
                                <div class="badge-glow"></div>
                            </div>
                            <div class="prize-details">
                                <h4>Huy Hiệu Độc Quyền</h4>
                                <div class="prize-value">50,000 VNĐ</div>
                                <p>Phần thưởng tiền mặt</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="event-cta" data-aos="fade-up" data-aos-delay="400">
                <a href="rankings/" class="btn btn-event">
                    <i class="fas fa-chart-line"></i>
                    Xem Bảng Xếp Hạng
                </a>
                <a href="classes/" class="btn btn-secondary">
                    <i class="fas fa-code"></i>
                    Tham Gia Ngay
                </a>
            </div>
        </div>

        <!-- Animated Background Elements -->
        <div class="event-bg-elements">
            <div class="bg-element trophy" style="--delay: 0s;">🏆</div>
            <div class="bg-element code" style="--delay: 1s;">💻</div>
            <div class="bg-element fire" style="--delay: 2s;">🔥</div>
            <div class="bg-element star" style="--delay: 0.5s;">⭐</div>
            <div class="bg-element rocket" style="--delay: 1.5s;">🚀</div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section footer-logo">
                    <div class="footer-logo-container">
                        <img src="assets/images/logo.jpg" alt="VTA Logo">
                        <div class="logo-text">Vthon Academy</div>
                    </div>
                    <div class="slogan">Học, học nữa, học mãi.</div>
                    <div class="footer-bottom">
                        <p>&copy; 2025 – All rights reserved.</p>
                    </div>
                </div>

                <div class="footer-section footer-contact">
                    <h3>Liên hệ</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> 0399787678</p>
                    <p><i class="fab fa-facebook-messenger"></i> Zalo: 0399787678</p>
                </div>

                <div class="footer-section footer-social">
                    <h3>Mạng xã hội</h3>
                    <div class="social-links">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="social-link facebook">
                            <i class="fab fa-facebook-f"></i> Facebook
                        </a>
                        <a href="https://www.tiktok.com/@sunnii39" target="_blank" class="social-link tiktok">
                            <i class="fab fa-tiktok"></i> TikTok
                        </a>
                        <a href="https://www.instagram.com/vlee.39/?hl=en" target="_blank" class="social-link instagram">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import the functions you need from the SDKs
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-analytics.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const auth = getAuth(app);

        // Check if user is logged in
        onAuthStateChanged(auth, (user) => {
            const loginButton = document.querySelector('.hero .btn');
            
            if (user) {
                // User is logged in
                loginButton.textContent = 'Vào Trang Cá Nhân';
            } else {
                // User is not logged in
                loginButton.textContent = 'Đăng Nhập';
            }
        });
    </script>

    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100
        });
    </script>

    <script src="assets/js/script.js"></script>
</body>
</html>